<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        Invoice Form <span class="text-primary">(UPDATE INVOICE)</span>
    </h6>
    <form class="mt-3" [formGroup]="dialogForm" (ngSubmit)="onSubmit(dialogForm)">
        <div class="row">
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Total</mat-label>
                    <input matInput type="text" placeholder="" formControlName="total">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Discount</mat-label>
                    <input matInput type="text" placeholder="" formControlName="discount">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Net</mat-label>
                    <input matInput type="text" placeholder="" formControlName="net">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Tax</mat-label>
                    <input matInput type="text" placeholder="" formControlName="tax">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Manual Discount</mat-label>
                    <input matInput type="text" placeholder="" formControlName="manualDiscount">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Bag Price</mat-label>
                    <input matInput type="text" placeholder="" formControlName="bageValue">
                </mat-form-field>
            </div>
            <div class="col-lg-4">
                <mat-form-field appearance="outline">
                    <mat-label>Subscription Type</mat-label>
                    <mat-select formControlName="subscriptionType" (selectionChange)="onSubscribeFromChange($event)">
                        <mat-option *ngFor="let item of subscribeOptions" [value]="item">
                            {{getSubscribeOptionsEnumKey(item) }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="col-lg-4" *ngIf="isSubscribeFromBranch">
                <mat-form-field appearance="outline">
                    <mat-label>Subscrip Branch</mat-label>
                    <mat-select formControlName="subscripBranch">
                        <mat-option *ngFor="let item of branches()?.data" [value]="item.branchID">{{ item.branchName }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="col-12">
                <mat-form-field appearance="outline">
                    <mat-label>Discount</mat-label>
                    <input matInput placeholder="20%" #DiscountInput (keydown.enter)="onApplyDiscount(DiscountInput)">
                    <button mat-mini-fab matSuffix class="me-3" color="custom" type="button"
                        (click)="onApplyDiscount(DiscountInput)">
                        <mat-icon>card_giftcard</mat-icon>
                    </button>
                </mat-form-field>
                <div *ngFor="let err of discountErrorList" class="text-danger">
                    <small>{{err}}</small>
                </div>
            </div>
            <div class="col-12" *ngIf="dialogForm.value.paymentDiscounts?.length">
                <table class="table table-bordered table-responsive table-light">
                    <thead>
                        <tr>
                            <th class="p-3 text-center">Code</th>
                            <th class="p-3 text-center">Value</th>
                            <th class="p-3 text-center">Delete</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of dialogForm.value.paymentDiscounts; let i = index">
                            <td class="p-3 text-center" style="vertical-align: middle;">
                                {{item.couponCode}}
                            </td>
                            <td class="p-3 text-center" style="vertical-align: middle;">
                                {{item.discountValue}}
                            </td>
                            <td class="p-3 text-center" style="vertical-align: middle;">
                                <button mat-mini-fab matSuffix class="me-3 text-danger" color="custom" type="button"
                                    (click)="removeDiscount(i)">
                                    <mat-icon>close</mat-icon>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-12">
                <mat-form-field appearance="outline">
                    <mat-label>Payment Methods</mat-label>
                    <mat-select formControlName="paymentMethods" multiple
                        (selectionChange)="onPaymentMethodsChange($event)">
                        <mat-option *ngFor="let item of PAYMENT_TYPE_DATA()?.data" [value]="item">{{
                            item.paymentName }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
                <table class="table table-bordered table-responsive table-custom"
                    *ngIf="dialogForm.value.paymentMethods && dialogForm.value.paymentMethods.length">
                    <thead>
                        <tr>
                            <th class="p-3 text-center">Payment Type</th>
                            <th class="p-3 text-center">Amount</th>
                            <th class="p-3 text-center">Reference Id</th>
                        </tr>
                    </thead>
                    <tbody formArrayName="payments">
                        <tr *ngFor="let payment of payments.controls; let i = index;" [formGroupName]="i">
                            <td class="p-0 text-center" style="vertical-align: middle;">
                                {{payment.get("methodName")?.value}}
                            </td>
                            <td class="p-0">
                                <mat-form-field>
                                    <mat-label>Enter Paid Amount</mat-label>
                                    <input matInput placeholder="$" required maxlength="9" formControlName="amount"
                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                </mat-form-field>
                            </td>
                            <td class="p-0">
                                <mat-form-field>
                                    <mat-label>Enter Transaction Reference Id</mat-label>
                                    <input matInput placeholder="#" required formControlName="refrenceId">
                                </mat-form-field>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-12 my-2">
                <div class="center text-center img-container">
                    <img *ngIf="currentUploadedFile != ''" [src]="currentUploadedFile" alt="Uploaded File">
                    <img *ngIf="dialogForm.value.refrence" [src]="'data:image/jpeg;base64,' + dialogForm.value.refrence"
                        alt="Uploaded File" (click)="openImageDialog(dialogForm.value.refrence)"
                        style="cursor: pointer; width: 200px; height: auto;">
                    <!-- modal لعرض الصورة بحجم أكبر -->
                    <div *ngIf="isModalOpen" class="modal-overlay" (click)="closeImageDialog()">
                        <div class="modal-content">
                            <img [src]="'data:image/jpeg;base64,' + selectedImage" alt="Uploaded File" />
                        </div>
                    </div>

                    <ngx-file-drop dropZoneLabel="Drop files here" (onFileDrop)="dropped($event)" accept="image/*"
                        dropZoneLabel="Drag & Drop to Upload Image File Here Or ..." [showBrowseBtn]="true"
                        [multiple]="false" browseBtnLabel="Choose Image">
                    </ngx-file-drop>
                </div>
            </div>
            <div class="col-12">
                <mat-form-field class="w-100 fs-16" appearance="outline">
                    <mat-label>Notes</mat-label>
                    <textarea matInput rows="3" placeholder="Write your notes" formControlName="notes"></textarea>
                </mat-form-field>
            </div>
        </div>
        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="dialogForm.invalid" color="success">Submit</button>
        </mat-dialog-actions>
    </form>
</div>