import { CommonModule } from "@angular/common";
import { Component, Inject } from "@angular/core";
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatDatepickerModule } from "@angular/material/datepicker";
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from "@angular/material/dialog";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { Store } from "@ngrx/store";
import { SelectSearchDirective } from "src/app/directives/select-search.directive";
import { ActionsService } from "src/app/services/actions.service";

@Component({
  selector: "app-hold",
  templateUrl: "./hold.component.html",
  styleUrls: ["./hold.component.scss"],
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatDialogModule,
    MatSelectModule,
    SelectSearchDirective,
    ReactiveFormsModule,
    MatInputModule,
    MatDatepickerModule,
    MatButtonModule,
  ],
})
export class HoldComponent {
  dialogForm = this._FormBuilder.group({
    StartHoldDate: ["", Validators.required],
    Notes: [""],
    SID: 0,
  });
  todayDate:Date = new Date();

  constructor(
    public _dialogRef: MatDialogRef<HoldComponent>,
    private _FormBuilder: FormBuilder,
    private _ActionsService: ActionsService,
    @Inject(MAT_DIALOG_DATA) public SID: number
  ) {}

  onSubmit(form: FormGroup) {
    if (form.valid) {
      this.dialogForm.patchValue({
        SID: this.SID,
        StartHoldDate: form.value.StartHoldDate.toLocaleDateString('en-CA'),
      });
      this._ActionsService.Hold(form.value).subscribe({
        next: () => {
          this._dialogRef.close(true);
        },
      });
    }
  }
}
