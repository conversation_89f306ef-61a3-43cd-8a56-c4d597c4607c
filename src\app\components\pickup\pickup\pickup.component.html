<style>
    .mat-mdc-form-field-flex {
  height: 50px !important;
}
</style>

<mat-card class="w-100">
    <mat-card-content>
        <h4 class="mb-3">Table Filters</h4>

        <div class="row">
            <div class="col-sm-6">
                <mat-form-field class="mx-1" appearance="outline" >
                    <input matInput [(ngModel)]="Sid"
                    type="tel"
                    inputmode="numeric"
                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                        (keyup.enter)="getsub(Sid);phonenumber='';sidList=[]" placeholder="Search By SID" #SID>
                        <button
                        mat-icon-button
                        matSuffix
                        (click)="getsub(Sid);phonenumber='';sidList=[]"
                        [attr.aria-label]="'search'"
                        >      <mat-icon>{{ 'search' }}</mat-icon>
                    </button>
                </mat-form-field>
            </div>
            <div class="col-sm-6">
                <mat-form-field class="mx-1" appearance="outline">
                    <input matInput [(ngModel)]="phonenumber" type="tel"
                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                        (keyup.enter)="onGetByPhone();Sid=''" placeholder="Search By Phone" #Phone>
                        <button
                        mat-icon-button
                        matSuffix
                        (click)="onGetByPhone();Sid=''"
                        [attr.aria-label]="'search'"
                        >      <mat-icon>{{ 'search' }}</mat-icon>
                    </button>
                </mat-form-field>
            </div>

            <div *ngIf="errorData!==''" class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>Warning!</strong>{{errorData}}.

            </div>
        </div>
        <div class="row">

            <div class="col-12 col-sm-6 col-md-4 col-lg-3" *ngFor="let subid of sidList">
                <mat-card class="pickup-card" [ngClass]="{'dark-appearance': subid.selected}">
                    <mat-card-content>
                        <!-- Sid with Icon -->
                        <div class="detail-item">
                            <mat-icon>person</mat-icon>
                            <span><strong>SID:</strong> {{ subid.sid }}</span>
                        </div>

                        <!-- RemainingDays with Icon -->
                        <div class="detail-item">
                            <mat-icon>calendar_today</mat-icon>
                            <span><strong>Remaining Days:</strong> {{ subid.remaingdays }}</span>
                        </div>

                        <!-- Status with Icon -->
                        <div class="detail-item">
                            <mat-icon>calendar_today</mat-icon>
                            <span><strong>Status:</strong> {{ subid.status }}</span>
                        </div>
                        <div class="detail-item">
                            <button mat-raised-button class="status-button"
                                style="background-color: chocolate;color: aliceblue;"
                                (click)="getsub(subid.sid);changeselecting(subid.sid)">
                                View
                            </button>
                        </div>

                    </mat-card-content>
                </mat-card>

            </div>

        </div>






    </mat-card-content>
</mat-card>

<mat-card class="subscription-card w-100" *ngIf="pivotedData && pivotedData.length>0">
    <mat-card-header>
        <mat-card-title>Subscription Details</mat-card-title>
    </mat-card-header>

    <mat-card-content>
        <!-- Material Card to contain the content -->
        <mat-card class="subscription-card">
            <mat-card-content>
                <div class="row">
                    <!-- Left Column -->
                    <div class="col-12 col-sm-6 ">
                        <div class="property">
                            <mat-icon>badge</mat-icon> <!-- Icon for Subscription ID -->
                            <p><strong>Subscription ID:</strong> {{ data?.subscriptionID }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>person</mat-icon> <!-- Icon for Customer Name -->
                            <p><strong>Customer Name:</strong> {{ data?.customerName }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>phone</mat-icon> <!-- Icon for Customer Phone -->
                            <p><strong>Customer Phone:</strong> {{ data?.customerPhone }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>business</mat-icon> <!-- Icon for Branch -->
                            <p><strong>Branch:</strong> {{ data?.branch }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>directions_car</mat-icon> <!-- Icon for Branch -->
                            <p><strong>Driver:</strong> {{ data?.driver }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>event</mat-icon> <!-- Icon for Branch -->
                            <p><strong>Last Pickedup Day:</strong> {{ data?.lastPickedUpDate |date }}</p>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-12 col-sm-6 ">
                        <div class="property">
                            <mat-icon>receipt</mat-icon> <!-- Icon for Plan -->
                            <p><strong>Plan:</strong> {{ data?.plan }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>calendar_today</mat-icon> <!-- Icon for Remaining Days -->
                            <p><strong>Remaining Days:</strong> {{ data?.remainingDays }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>note</mat-icon> <!-- Icon for Notes -->
                            <p><strong>Notes:</strong> {{ data?.notes }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>event</mat-icon> <!-- Icon for Created At -->
                            <p><strong>Created At:</strong> {{ data?.createAt | date }}</p>
                        </div>
                        <div class="property">
                            <mat-icon>credit_card</mat-icon> <!-- Icon for Created At -->
                            <p  ><strong>Status:</strong> {{ data?.status  }}</p>
                        </div>
                        <div class="property">
                          
                            <p [ngClass]="{'pickedup':data?.sidtype=='PickUp','prepared':data?.sidtype=='Prepared'}"> {{ data?.sidtype  }}</p>

                        </div>
                    </div>
                </div>
            </mat-card-content>
        </mat-card>
        <mat-card *ngIf="pickupday && pickupday.length>0">
            <mat-card-content>
                <div class="row">
                    <div class="mb-2">
                        <h5>PickUp Day:{{pickupday?.[0]?.deliveryDate|date}}</h5>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3"
                        *ngFor="let mealtypeGroup of (pickupday||[] | groupBy: 'mealType')">
                        <h5 style="color:rgb(255, 136, 0)">{{ mealtypeGroup.key }}</h5>
                        <div *ngFor="let meal of mealtypeGroup.value">
                            <p>{{ meal.mealName }}</p>
                        </div>
                    </div>
                </div>
                <button mat-raised-button color="primary" (click)="onPickup()">Pickup</button>
            </mat-card-content>
        </mat-card>
        <!-- Table to show the meal delivery details -->
        <mat-card>
            <mat-card-header>
                <mat-card-title>Subscription Days</mat-card-title>
            </mat-card-header>

            <table mat-table [dataSource]="pivotedData">
                <ng-container matColumnDef="deliveryDate">
                    <th mat-header-cell *matHeaderCellDef>Delivery Date</th>
                    <td mat-cell *matCellDef="let row">{{ row.deliveryDate|date }}</td>
                </ng-container>

                <!-- deliveryState column with colored buttons -->
                <ng-container matColumnDef="deliveryState">
                    <th mat-header-cell *matHeaderCellDef>Delivery State</th>
                    <td mat-cell *matCellDef="let row">
                        <button mat-raised-button
                            [ngStyle]="{ 'background-color': getDeliveryStatusColor(row.deliveryState) }"
                            class="status-button">
                            {{ row.deliveryState }}
                        </button>
                    </td>
                </ng-container>

                <!-- Dynamically generate columns for each unique MealType -->
                <ng-container *ngFor="let mealType of displayedColumns.slice(2)" matColumnDef="{{ mealType }}">
                    <th mat-header-cell *matHeaderCellDef>{{ mealType }}</th>
                    <td mat-cell *matCellDef="let row">{{ row[mealType] || '' }}</td>
                </ng-container>

                <!-- Table header and body -->
                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

        </mat-card>

    </mat-card-content>
</mat-card>