import { Component, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule, MatDateRangeInput } from '@angular/material/datepicker';
import { materialize, subscribeOn } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatTableModule } from '@angular/material/table';
import { TableComponent } from 'src/app/pages/table/table.component';
import { FeatherModule } from 'angular-feather';
import { pickupService } from 'src/app/services/pickup.service';
import { IpickupRespons, IRespons, sidList, SubDetail, Subscription } from 'src/app/interfaces/pickup.Interface';
import { MatDividerModule } from '@angular/material/divider';
import { FormsModule, NgModel } from '@angular/forms';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/pages/confirmationDialog/confirmationDialog.component';
import { GroupByPipe } from 'src/app/pipes/GroupByPipe ';
import { DateFormatPipe } from 'src/app/pipes/longdate';
import { SubdetailsComponent } from "./suddetails/subdetails/subdetails.component";


@Component({
  selector: 'app-pickup',
  standalone: true,
  imports: [CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatButtonModule,
    MatInputModule,
    MatTableModule,
    TableComponent,
    FeatherModule,
    MatDividerModule,
    FormsModule,
    MatIconModule,
    MatDialogModule,
    GroupByPipe,
    DateFormatPipe, SubdetailsComponent],
  templateUrl: './pickup.component.html',
  styleUrls: ['./pickup.component.scss'],
})
export class PickupComponent {
  replaceDate(arg0: any): string | number | Date {
    return arg0.replace(/-\d+$/, '');
  }
  changeselecting(sid: number) {
    if (this.sidList && this.sidList.length > 0) {
      this.sidList.forEach(x => x.selected = false);
      let selecteditem = this.sidList.find(x => x.sid === sid);
      if (selecteditem) {
        selecteditem.selected = true;
      }
    }
  }

  phonenumber: string = '';
  sidList: sidList[] = [];
  stat: boolean = false;
  errorData: string = '';
  Sid: any;
  pickedID: any;
  isToday: boolean = false;
  DateFrom: any;
  data?: Subscription;
  pickupday?: SubDetail[] = [];
  pivotedData: any[] = [];
  displayedColumns: string[] = [];
  pickucolums: string[] = ['deliveryDate', 'mealType', 'mealName', 'deliveryState'];

  pickup() {
    const day = this.pickupday && this.pickupday[0] && this.pickupday[0].deliveryDate || undefined
    if (day) {
      this.pickupservice.pickup(this.pickedID, day).subscribe(
        response => {
          console.log('Pickup successful', response);
          this.getsub(this.pickedID);
        },
        error => {
          console.error('Pickup failed', error);
        });
    }
  }
  constructor(private pickupservice: pickupService, public dialog: MatDialog) { }



  getsub(sid: number) {
    this.pickupservice.getSubscrption(sid).subscribe((res: IpickupRespons) => {
      this.data = res.data as Subscription;
      this.errorData = '';

      if (this.data?.subDetails) {
        const today = new Date();
        today.setHours(0, 0, 0, 0); 

        this.transformData(this.data.subDetails);
        this.isToday = this.data.subDetails.some(x => new Date( x.deliveryDate).getDate() === today.getDate()); // Use some to return a boolean
        const foundPickupDay = this.data.subDetails.filter(x =>  new Date( x.deliveryDate).getDate() === today.getDate() && x.deliveryState == 'Prepared');
        this.pickupday = foundPickupDay ? foundPickupDay : [];
        this.pickedID = this.data.subscriptionID;
   
      }
    },
      (error) => {
        this.reset();
        this.errorData = error;
      });

  }


  transformData(subDetails: SubDetail[]): void {
    const groupedByDeliveryDate = this.groupByDeliveryDateAndDayId(subDetails);
    console.log(groupedByDeliveryDate);
    // Determine meal types (headers)
    const mealTypes = this.getUniqueMealTypes(subDetails);
    this.displayedColumns = ['deliveryDate', 'deliveryState', ...mealTypes];
    // Create pivoted data
    this.pivotedData = Object.keys(groupedByDeliveryDate).map((deliveryDate) => {
      const row: { [key: string]: string } = { deliveryDate };
      // Add delivery state button
      row['deliveryState'] = groupedByDeliveryDate[deliveryDate][0]?.deliveryState;
      // For each meal type, add the corresponding meal name
      mealTypes.forEach(mealType => {
        const meal = groupedByDeliveryDate[deliveryDate].find(
          item => item.mealType === mealType
        );
        row[mealType] = meal ? meal.mealName : '';  // If meal is not found, put an empty string
      });
      
      return row;
    });
    this.pivotedData.forEach(x=>x.deliveryDate=x.deliveryDate.replace(/-\d+$/, ''));
    this.pivotedData.sort((a,b)=>{
      return new Date(a.deliveryDate).getTime()-new Date(b.deliveryDate).getTime()
    })
    console.log(this.pivotedData);
  }

  // Group subDetails by deliveryDate
  groupByDeliveryDate(subDetails: SubDetail[]): { [key: string]: SubDetail[] } {
    return subDetails.reduce((result: { [key: string]: SubDetail[] }, currentValue) => {
      const deliveryDate = currentValue.deliveryDate;
      if (!result[deliveryDate]) {
        result[deliveryDate] = [];
      }
      result[deliveryDate].push(currentValue);
      return result;
    }, {});
  }

  groupByDeliveryDateAndDayId(subDetails: SubDetail[]): { [key: string]: SubDetail[] } {
    return subDetails.reduce((result: { [key: string]: SubDetail[] }, currentValue) => {
      // Create a composite key using both deliveryDate and dayId
      const key = `${currentValue.deliveryDate}-${currentValue.daynumber}`;

      // Initialize the array for this key if it doesn't exist
      if (!result[key]) {
        result[key] = [];
      }

      // Push the current item into the appropriate group
      result[key].push(currentValue);
      return result;
    }, {});
  }


  // Get unique meal types
  getUniqueMealTypes(subDetails: SubDetail[]): string[] {
    const mealTypes = subDetails.map(detail => detail.mealType);
    return Array.from(new Set(mealTypes));  // Get unique meal types
  }
  // Mapping delivery status to unique colors
  deliveryStatusColorMap = {
    Pending: '#2196F3',    // Blue
    Deliveried: '#4CAF50', // Green
    NotDelivered: '#F44336', // Red
    Hold: '#FFC107',        // Amber/Yellow
    Resticited: '#FF9800',  // Orange
    Canceld: '#9E9E9E',     // Grey
    AllStatus: '#673AB7',    // Deep Purple
    PickedUp: '#61737B',    // Yellow
    Refund: '#E91E63',      // Pink
    Prepared: '#00BCD4',    // Cyan
  };


  // Get color for delivery state
  getDeliveryStatusColor(status: DeliveryStatus): string {
    return this.deliveryStatusColorMap[status] || 'gray';  // Default to gray if status not found
  }

  today(): string {
    const todayLocal = new Date();
    const day = todayLocal.getDate();
    const month = String(todayLocal.getMonth() + 1).padStart(2, '0');
    const year = todayLocal.getFullYear();

    // Format as 'dd-MM-yyyy'
    const todayFormatted = `${year}-${month}-${day}`;
    return todayFormatted;
  }

  onPickup(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: { message: 'pickup' },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.pickup();
      } else {
      }
    });
  }


  onGetByPhone() {
    this.pickupservice.getSubscrptionByPhone(this.phonenumber).subscribe((res: IRespons<sidList>) => {
      res.data.forEach((item) => {
        item.selected = false;
      });

      this.errorData = '';
      this.sidList = res.data;

      if (this.sidList && this.sidList.length > 1) {
        const sidObject = this.sidList[this.sidList.length - 1];
        sidObject.selected = true;
        this.getsub(sidObject.sid);
        this.pickedID = sidObject.sid;
      }
      if (this.sidList && this.sidList.length == 1) {
        this.getsub(this.sidList[0].sid);
        this.pickedID = this.sidList[0].sid;
        this.sidList[0].selected = true;
      }

    },
      (error) => {
        this.reset();
        this.errorData = error;
      });
  }

  reset() {

    this.pivotedData = [];
    this.pickupday = [];
    this.sidList = [];
  }
}

export enum DeliveryStatus {
  Pending = 'Pending',
  Deliveried = 'Deliveried',
  NotDelivered = 'NotDelivered',
  Hold = 'Hold',
  Resticited = 'Resticited',
  Canceld = 'Canceld',
  AllStatus = 'AllStatus',
  PickedUp = 'PickedUp',
  Refund = 'Refund',
  Prepared = 'Prepared',
}