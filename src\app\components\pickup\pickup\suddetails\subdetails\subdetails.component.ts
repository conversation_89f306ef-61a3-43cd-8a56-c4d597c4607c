import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { TableComponent } from 'src/app/pages/table/table.component';

@Component({
  selector: 'app-subdetails',
  standalone: true,
   imports: [CommonModule,

      MatTableModule,
      TableComponent,

    ],
 
  templateUrl: './subdetails.component.html',
  styleUrls: ['./subdetails.component.scss']
})
export class SubdetailsComponent {
  displayedColumns: string[] = ['position', 'name', 'symbol'];
  dataSource = ELEMENT_DATA;

  getMergedData(data: any) {
    const mergedData:any = [];
    let lastElement:any = null;

    data.forEach((item: any, index: number) => {
      if (lastElement && lastElement.name === item.name) {
        mergedData[mergedData.length - 1].count += 1; // Increase count if it's the same name
      } else {
        item.count = 1; // Set count for new data
        mergedData.push(item);
      }
      lastElement = item;
    });
console.log('this is',mergedData);
    return mergedData;
  }
}
interface PeriodicElement {
  position: number;
  name: string;
  symbol: string;
}

const ELEMENT_DATA: PeriodicElement[] = [
  {position: 1, name: 'Hydrogen', symbol: 'H'},
  {position: 2, name: 'Hydrogen', symbol: 'H'},
  {position: 3, name: 'Helium', symbol: 'He'},
  {position: 4, name: 'Helium', symbol: 'He'},
  {position: 5, name: 'Lithium', symbol: 'Li'},
];