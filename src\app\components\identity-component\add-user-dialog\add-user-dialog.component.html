<div class="p-5">
    <div class="cdk-global-overlay-wrapper w-100 h-100" dir="ltr" style="justify-content: center; align-items: center;">
        <div id="cdk-overlay-5" class="cdk-overlay-pane w-100 h-100" style=" position: static;">
            <div tabindex="-1" class="mat-mdc-dialog-container mdc-dialog cdk-dialog-container mdc-dialog--open"
                id="mat-mdc-dialog-1" role="dialog" aria-modal="true" aria-labelledby="mat-mdc-dialog-title-1"
                style="--mat-dialog-transition-duration: 0ms;">
                <div class="mdc-dialog__container">
                    <div class="mat-mdc-dialog-surface mdc-dialog__surface">
                        <div class="ng-star-inserted">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 mat-dialog-title=""
                                    class="mat-mdc-dialog-title mdc-dialog__title mat-subtitle-1 ps-3">
                                    Create User</h5>
                                <mat-icon class="text-secondary close-modal-button" (click)="closeModal()" matSuffix>
                                    close</mat-icon>
                            </div>
                            <div mat-dialog-content=""
                                class="mat-mdc-dialog-content mdc-dialog__content mat-subtitle-2 lh-16 py-0">
                                Fill all required inputs.
                            </div>
                            <div class="px-5 py-2">
                                <form [formGroup]="createForm" (ngSubmit)="onSubmit(createForm)">
                                    <div class="row">
                                        <div class="col-12 mb-2 flex-between">
                                            <mat-slide-toggle class="mx-2" [autofocus]="false"
                                                formControlName="autoConfirmEmail">Confirm
                                                Email</mat-slide-toggle>
                                            <mat-slide-toggle class="mx-2" formControlName="activateUser">Activate
                                                User</mat-slide-toggle>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>First Name</mat-label>
                                                <input matInput type="text" placeholder="Enter first name"
                                                    formControlName="firstName">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Last Name</mat-label>
                                                <input matInput type="text" placeholder="Enter last name"
                                                    formControlName="lastName">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Username</mat-label>
                                                <input matInput type="text" placeholder="Enter User name"
                                                    formControlName="userName">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Email</mat-label>
                                                <input matInput type="email" placeholder="Enter User email address"
                                                    formControlName="email">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Phone Number</mat-label>
                                                <input matInput type="text" placeholder="Enter Phone Number"
                                                    formControlName="phoneNumber" maxlength="18"
                                                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>System ID</mat-label>
                                                <input matInput type="text" placeholder="Enter System ID"
                                                    formControlName="systemId" maxlength="5"
                                                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Password</mat-label>
                                                <input matInput type="password" placeholder="Enter Password"
                                                    formControlName="password">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Confirm Password</mat-label>
                                                <input matInput type="password" placeholder="Enter Confirm Password"
                                                    formControlName="confirmPassword">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Job Title</mat-label>
                                                <input matInput type="text" placeholder="Enter Job Title"
                                                    formControlName="jobTitle">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Branch</mat-label>
                                                <mat-select formControlName="defaultBranchId">
                                                    <input type="text" autocomplete="off" matInput appSelectSearch
                                                        [searchList]="Branchies|| []"
                                                        [filterKeys]="['name']" class="px-3 py-4 mat-filter-menu"
                                                        style="font-size: 16px;font-weight: 400;" placeholder="Search..." />
                                                    <mat-option *ngFor="let item of Branchies"
                                                        [value]="item.branchID">
                                                        {{item.branchName}}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                    </div>



                                    <div mat-dialog-actions=""
                                        class="mat-mdc-dialog-actions mdc-dialog__actions p-24 p-t-0 justify-content-center">
                                        <button mat-raised-button type="submit" color="primary"
                                            [disabled]="createForm.invalid">Create</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>