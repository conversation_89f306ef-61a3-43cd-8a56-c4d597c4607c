<div class="p-5">
    <div class="cdk-global-overlay-wrapper w-100 h-100" dir="ltr" style="justify-content: center; align-items: center;">
        <div id="cdk-overlay-5" class="cdk-overlay-pane w-100 h-100" style=" position: static;">
            <div tabindex="-1" class="mat-mdc-dialog-container mdc-dialog cdk-dialog-container mdc-dialog--open"
                id="mat-mdc-dialog-1" role="dialog" aria-modal="true" aria-labelledby="mat-mdc-dialog-title-1"
                style="--mat-dialog-transition-duration: 0ms;">
                <div class="mdc-dialog__container">
                    <div class="mat-mdc-dialog-surface mdc-dialog__surface">
                        <div class="ng-star-inserted">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 mat-dialog-title=""
                                    class="mat-mdc-dialog-title mdc-dialog__title mat-subtitle-1 ps-3">
                                    Create Or Update Customer</h5>
                                <mat-icon class="text-secondary close-modal-button" (click)="closeModal()" matSuffix>
                                    close</mat-icon>
                            </div>
                            <div mat-dialog-content=""
                                class="mat-mdc-dialog-content mdc-dialog__content mat-subtitle-2 lh-16 py-0">
                                Fill all required inputs.
                            </div>
                            <div class="px-5 py-2">
                                <form [formGroup]="customerForm" (ngSubmit)="onSubmit(customerForm)">
                                    <div class="row">
                                        <div class="col-12 mb-2 text-right">
                                            <mat-slide-toggle formControlName="status">Status</mat-slide-toggle>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Name</mat-label>
                                                <input matInput type="text" placeholder="Enter customer name"
                                                    formControlName="customerName">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Email</mat-label>
                                                <input matInput type="email" placeholder="Enter customer email"
                                                    formControlName="email">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Height</mat-label>
                                                <input matInput type="text" placeholder="Enter customer height"
                                                    formControlName="height" maxlength="3"
                                                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Weight</mat-label>
                                                <input matInput type="text" placeholder="Enter customer weight"
                                                    formControlName="weight" maxlength="3"
                                                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Register Type</mat-label>
                                                <mat-select formControlName="regType">
                                                    <mat-option *ngFor="let item of Regtype" [value]="item">{{ item }}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Customer Type</mat-label>
                                                <mat-select formControlName="customerType">
                                                    <mat-option *ngFor="let item of customerType" [value]="item">
                                                        {{item}}</mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Category</mat-label>
                                                <mat-select formControlName="categoryId">
                                                    <mat-option *ngFor="let item of category" [value]="item?.id">{{
                                                        item?.categoryName }}</mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>

                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Area</mat-label>
                                                <mat-select multiple formControlName="customerAdresses">
                                                    <input type="text" autocomplete="off" matInput appSelectSearch
                                                        [searchList]="area" [filterKeys]="['areaName', 'branchName']"
                                                        class="px-3 py-4 mat-filter-menu" placeholder="Search..."
                                                        style="font-size: 16px;font-weight: 400;" />
                                                    <mat-option *ngFor="let item of area" [value]="item">
                                                        {{ item?.areaName }} - {{ item?.branchName }}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>

                                        <div class="col-lg-4 col-md-6">
                                            <mat-form-field class="example-full-width" appearance="outline">
                                                <mat-label>Choose customer birth date</mat-label>
                                                <input (click)="picker.open()" matInput [matDatepicker]="picker"
                                                    formControlName="birthDate">
                                                <mat-datepicker-toggle matIconSuffix [for]="picker">
                                                </mat-datepicker-toggle>
                                                <mat-datepicker touchUi #picker></mat-datepicker>
                                            </mat-form-field>
                                        </div>

                                        <div class="col-12"
                                            *ngIf="customerForm.value.customerAdresses && customerForm.value.customerAdresses.length">
                                            <mat-card>
                                                <mat-card-content>
                                                    <mat-card-title>Complete Customer Address</mat-card-title>
                                                    <mat-card-subtitle>Area / Branch / Address</mat-card-subtitle>
                                                    <mat-form-field class="w-100 fs-16" appearance="outline"
                                                        *ngFor="let area of customerForm.value.customerAdresses; let i = index">
                                                        <mat-label>{{area.areaName}} - {{area.branchName}}</mat-label>
                                                        <input matInput type="text" #areaInput
                                                            [value]="this.customerForm.value.customerAdresses[i].Adress || ''"
                                                            (blur)="addFullAddress(area,areaInput.value)"
                                                            placeholder="Add Customer Address">
                                                    </mat-form-field>
                                                </mat-card-content>
                                            </mat-card>
                                        </div>


                                        <ng-container formGroupName="customerPhons">
                                            <div class="col-lg-3 col-md-6">
                                                <mat-form-field class="w-100 fs-16" appearance="outline">
                                                    <mat-label>Mobile</mat-label>
                                                    <input matInput type="text" formControlName="Mobile"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                                    <mat-icon class="text-secondary" matSuffix>phone</mat-icon>
                                                </mat-form-field>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <mat-form-field class="w-100 fs-16" appearance="outline">
                                                    <mat-label>Work Phone</mat-label>
                                                    <input matInput type="text" formControlName="Work Phone"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                                    <mat-icon class="text-secondary" matSuffix>phone</mat-icon>
                                                </mat-form-field>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <mat-form-field class="w-100 fs-16" appearance="outline">
                                                    <mat-label>Home Phone</mat-label>
                                                    <input matInput type="text" formControlName="Home Phone"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                                    <mat-icon class="text-secondary" matSuffix>phone</mat-icon>
                                                </mat-form-field>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <mat-form-field class="w-100 fs-16" appearance="outline">
                                                    <mat-label>Other Phone</mat-label>
                                                    <input matInput type="text" formControlName="Other Phone"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
                                                    <mat-icon class="text-secondary" matSuffix>phone</mat-icon>
                                                </mat-form-field>
                                            </div>
                                        </ng-container>

                                        <div class="col-12">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Notes</mat-label>
                                                <textarea matInput rows="2" placeholder="Write your notes"
                                                    value="Default Value" formControlName="notes"></textarea>
                                            </mat-form-field>
                                        </div>
                                    </div>

                                    <div mat-dialog-actions=""
                                        class="mat-mdc-dialog-actions mdc-dialog__actions p-24 p-t-0 justify-content-center">
                                        <button mat-flat-button="" color="primary" [disabled]="customerForm.invalid"
                                            class="mdc-button mdc-button--unelevated mat-mdc-unelevated-button mat-primary mat-mdc-button-base">
                                            <span class="mdc-button__label"> Submit </span></button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>