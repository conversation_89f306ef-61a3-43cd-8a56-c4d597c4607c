import { Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';

@Pipe({
  name: 'dateFormat',
  standalone:true
})
export class DateFormatPipe implements PipeTransform {
  constructor(private datePipe: DatePipe) {}

  transform(value: string | Date): string | null {
    // Ensure the value is a valid string
    if (typeof value === 'string') {
      // Remove any numeric suffix like -1, -2, -3 using a regular expression
      value = value.replace(/-\d+$/, '');
    }

    // Parse the cleaned-up value as a date
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return null; // Return null if the date is invalid
    }

    // Use DatePipe to format the date as 'dddd, MMMM d, yyyy'
    return this.datePipe.transform(date, 'fullDate'); // 'fullDate' format gives "dddd, MMMM d, yyyy"
  }
}
