{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Flexy-admin-angular-lite": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}, "@schematics/angular:component": {"style": "scss", "skipTests": true, "standalone": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true, "standalone": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true, "standalone": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["apexcharts"], "outputPath": "dist/flexy-admin-angular-lite", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "src/assets/styles/style.scss", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500mb", "maximumError": "500mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "Flexy-admin-angular-lite:build:production"}, "development": {"browserTarget": "Flexy-admin-angular-lite:build:development"}}, "defaultConfiguration": "development", "options": {"host": "dashboard.test.localhost"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Flexy-admin-angular-lite:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}