import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'groupBy',
  standalone:true
})
export class GroupByPipe implements PipeTransform {
  transform(array: any[], property: string): any[] {
    if (!array || !property) return array;

    const grouped = array.reduce((acc, item) => {
      const key = item[property];
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {});

    return Object.keys(grouped).map(key => ({ key, value: grouped[key] }));
  }
}