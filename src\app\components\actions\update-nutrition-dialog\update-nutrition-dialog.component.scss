.input-row {
  display: flex;
  padding: 1.75rem 0;
  border-bottom: 1px solid #ffcfc0;
}

.input-row:last-child {
  border-bottom: 0;
}

.title {
  margin-right: 2rem;
}

.label {
  margin-bottom: 0.25rem;
  font-weight: bold;
}

.description {
  color: #576674;
}

.input {
  display: flex;
  align-items: center;
  margin-left: auto;
}

button.nutrition-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 3rem;
  height: 3rem;
  border: 1px solid #fb9778;
  border-radius: 1000px;
  background-color: white;
}

button.minus {
  display: flex;
  justify-content: center;
  align-items: normal;
  padding-top: 5px;
}

button.nutrition-btn:hover {
  background-color: #ffcfc0;
  cursor: pointer;
}

button.nutrition-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.25rem #ffcfc0;
}

button.nutrition-btn[disabled] {
  opacity: 0.5;
  pointer-events: none;
}

button.nutrition-btn:active {
  background-color: #ffcfc0;
}

.number {
  font-size: 1.75rem;
  min-width: 3rem;
  text-align: center;
}

.icon {
  user-select: none;
}

.dim {
  color: #8d9ca7;
}
