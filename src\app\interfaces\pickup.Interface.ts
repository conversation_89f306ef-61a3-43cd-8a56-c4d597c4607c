// subscription.interface.ts

export interface SubDetail {
    deliveryDate: string; // ISO format date string
    deliveryState: string;
    mealName: string;
    mealType: string;
    daynumber:number
  }
  
  export interface Subscription {
    subscriptionID: number;
    customerName: string;
    customerPhone: string;
    createAt: string; // ISO format date string
    lastPickedUpDate: string;
    mealTypes: string;
    notes: string;
    branch: string;
    plan: string;
    remainingDays: number;
    subDetails: SubDetail[];
    driver :string;
    sidtype:string;
    status:string;
  }
  
  export interface IpickupRespons
  {
    data:Subscription;
    message: string[];
    success:boolean;
  }
  export interface IRespons<T>
  {
    data:T[];
    message: string[];
    success:boolean;
  }
  export interface sidList
  {
    sid:number;
    remaingdays:number;
    status:string;
    selected:boolean;
  }