{"name": "flexy-admin-angular-lite", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.0.0", "@angular/cdk": "^16.0.1", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/language-service": "^16.0.9", "@angular/material": "^16.0.1", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@ngrx/effects": "^16.3.0", "@ngrx/store": "^16.3.0", "angular-feather": "^6.5.0", "apexcharts": "^3.40.0", "bootstrap": "^5.3.2", "crypto-js": "^4.1.1", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jwt-decode": "^4.0.0", "mat-table-exporter": "^15.0.0", "ng-apexcharts": "^1.7.6", "ngx-file-drop": "^16.0.0", "ngx-online-status": "^2.0.0", "ngx-ui-loader": "^13.0.0", "rxjs": "~7.8.0", "secure-web-storage": "^1.0.2", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.1", "@angular/cli": "~16.0.1", "@angular/compiler-cli": "^16.0.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "sass": "~1.80.6", "typescript": "~5.0.4"}}