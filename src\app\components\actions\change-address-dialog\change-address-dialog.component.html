<div class="p-5">
    <div class="cdk-global-overlay-wrapper w-100 h-100" dir="ltr" style="justify-content: center; align-items: center;">
        <div id="cdk-overlay-5" class="cdk-overlay-pane w-100 h-100" style=" position: static;">
            <div tabindex="-1" class="mat-mdc-dialog-container mdc-dialog cdk-dialog-container mdc-dialog--open"
                id="mat-mdc-dialog-1" role="dialog" aria-modal="true" aria-labelledby="mat-mdc-dialog-title-1"
                style="--mat-dialog-transition-duration: 0ms;">
                <div class="mdc-dialog__container">
                    <div class="mat-mdc-dialog-surface mdc-dialog__surface">
                        <div class="ng-star-inserted">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 mat-dialog-title=""
                                    class="mat-mdc-dialog-title mdc-dialog__title mat-subtitle-1 ps-3">
                                    Update Subscription Header Info</h5>
                                <mat-icon class="text-secondary close-modal-button me-3 pointer" (click)="closeModal()"
                                    matSuffix>
                                    close</mat-icon>
                            </div>
                            <div mat-dialog-content=""
                                class="mat-mdc-dialog-content mdc-dialog__content mat-subtitle-2 lh-16 py-0">
                                You can select the primary subscription address.
                            </div>
                            <div class="px-5 py-3">
                                <form [formGroup]="editForm" (ngSubmit)="onSubmit(editForm)">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <mat-form-field appearance="outline">
                                                <mat-label>Area</mat-label>
                                                <mat-select multiple formControlName="customerAdresses">
                                                    <input type="text" autocomplete="off" matInput appSelectSearch
                                                        [searchList]="area" [filterKeys]="['areaName', 'branchName']"
                                                        class="px-3 py-4 mat-filter-menu" placeholder="Search..."
                                                        style="font-size: 16px;font-weight: 400;" />
                                                    <mat-option *ngFor="let item of area" [value]="item">
                                                        {{ item?.areaName }} - {{ item?.branchName }}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                        <div class="col-12"
                                            *ngIf="editForm.value.customerAdresses && editForm.value.customerAdresses.length">
                                            <mat-card>
                                                <mat-card-content>
                                                    <mat-card-title>Complete Customer Address</mat-card-title>
                                                    <mat-card-subtitle>Area / Branch / Address</mat-card-subtitle>
                                                    <div class="d-flex" style="align-items: baseline;"
                                                        *ngFor="let customerAddress of editForm.value.customerAdresses; let i = index">
                                                        <mat-radio-button class="example-radio-button"
                                                            (change)="onAreaSelectionChange(customerAddress.id)"
                                                            [checked]="editForm.value.selectedAddress === customerAddress.id"
                                                            [value]="customerAddress.id"></mat-radio-button>
                                                        <mat-form-field class="w-100 fs-16" appearance="outline">
                                                            <mat-label>{{customerAddress.areaName}} -
                                                                {{customerAddress.branchName}}</mat-label>
                                                            <input matInput type="text" #areaInput
                                                                (blur)="addNewAddress(areaInput.value, customerAddress.id)"
                                                                [value]="customerAddress.adress || ''"
                                                                placeholder="Add Customer Address">
                                                        </mat-form-field>
                                                    </div>
                                                </mat-card-content>
                                            </mat-card>
                                        </div>
                                    </div>
                                    <div
                                        class="mat-mdc-dialog-actions mdc-dialog__actions p-24 p-t-0 justify-content-end">
                                        <button mat-raised-button color="success"
                                            [disabled]="editForm.invalid">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>