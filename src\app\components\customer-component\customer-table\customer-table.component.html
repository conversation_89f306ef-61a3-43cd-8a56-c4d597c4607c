<div class="my-5 mx-5">
    <mat-card class="w-100">
        <mat-card-content>
            <h4 class="mb-3">Table Filters</h4>
            <div class="row">
                <div class="col-12">
                    <mat-form-field class="mx-1" appearance="outline">
                        <mat-label>Search By Customer Phone</mat-label>
                        <input matInput
                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                            (keyup.enter)="filter(Phone)" (blur)="filter(Phone)" placeholder="Ex: +02" #Phone>
                        <mat-icon (click)="openCreateDialog()" class="text-success pointer"
                            matSuffix>library_add</mat-icon>
                    </mat-form-field>
                </div>
            </div>
        </mat-card-content>
    </mat-card>
    <mat-card class="w-100">
        <mat-card-content>
            <h4 class="mb-56">Customers Table</h4>
            <generic-table [TABLE_DATA]="TABLE_DATA()" [columns]="columns" [pageSizeOptions]="[10, 25, 50, 100, 500]"
                [components]="{filter: false,columns: false,export: false,selection: false}"
                (clicked)="clickedRow($event)" (PaginateOptions)="Paginate($event)">
            </generic-table>
        </mat-card-content>
    </mat-card>
</div>