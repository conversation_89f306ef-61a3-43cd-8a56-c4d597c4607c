<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        City Form
    </h6>
    <form class="mt-3" [formGroup]="dialogForm" (ngSubmit)="onSubmit(dialogForm)">
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>City Name</mat-label>
            <input matInput type="text" placeholder="Enter City Name" formControlName="name">
        </mat-form-field>
        <mat-form-field appearance="outline">
            <mat-label>Governorate</mat-label>
            <mat-select formControlName="goverID">
                <input type="text" autocomplete="off" matInput appSelectSearch
                    [searchList]="GOVERNORATE_DATA()?.data || []"
                    [filterKeys]="['governorateName']" class="px-3 py-4 mat-filter-menu"
                    style="font-size: 16px;font-weight: 400;"
                    placeholder="Search..." />
                <mat-option *ngFor="let item of GOVERNORATE_DATA()?.data"
                    [value]="item.id">
                    {{item.governorateName}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="dialogForm.invalid" color="success">Submit</button>
        </mat-dialog-actions>
    </form>
</div>