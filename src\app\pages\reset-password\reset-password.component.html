<div class="blank-layout-container justify-content-center">
    <div class="position-relative row w-100 h-100">
        <div class="col-lg-7 col-xl-8 bg-gredient p-0">
            <div class="p-24 h-100"><a class="ng-star-inserted"><img src="assets/images/logo-dark.svg" alt="logo"
                        class="align-middle m-2"></a>
                <div class="align-items-center justify-content-center img-height d-none d-lg-flex"><img
                        src="assets/images/login-bg.svg" alt="login" style="width: 100%; max-width: 600px;">
                </div>
            </div>
        </div>
        <div class="col-lg-5 col-xl-4 p-0">
            <div class="p-32 d-flex align-items-start align-items-lg-center justify-content-center h-100">
                <div class="row justify-content-center w-100">
                    <div class="col-lg-9 max-width-form">
                        <h4 class="f-w-700 f-s-24 m-0">Reset Password</h4><span
                            class="f-s-16 d-block mat-body-1 m-t-16">Follow these steps to safely reset
                            your account password and regain access to your account.</span>
                        <form [formGroup]="authForm" (ngSubmit)="onSubmit(authForm)" class="m-t-30 p-t-20">
                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                <mat-label>Password</mat-label>
                                <input matInput type="password" placeholder="Enter your password"
                                    formControlName="password"
                                    [class.is-invalid]="authForm.get('password')?.errors != null && authForm.get('password')?.touched">
                                <mat-icon class="text-secondary" matSuffix>lock</mat-icon>
                            </mat-form-field>
                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                <mat-label>Confirm Password</mat-label>
                                <input matInput type="password" placeholder="Enter your password again"
                                    formControlName="confirmPassword"
                                    [class.is-invalid]="authForm.get('confirmPassword')?.errors != null && authForm.get('confirmPassword')?.touched">
                                <mat-icon class="text-secondary" matSuffix>password</mat-icon>
                            </mat-form-field>
                            <button mat-flat-button="" ngClass="w-100" color="primary"
                                [disabled]="authForm.invalid">Reset</button>
                            <a mat-stroked-button="" color="primary"
                                class="w-100 m-t-8 mdc-button mdc-button--outlined mat-mdc-outlined-button mat-primary mat-mdc-button-base"
                                mat-ripple-loader-class-name="mat-mdc-button-ripple" routerLink="../login"><span
                                    class="mat-mdc-button-persistent-ripple mdc-button__ripple"></span><span
                                    class="mdc-button__label"> Back to Login </span><span
                                    class="mat-mdc-focus-indicator"></span><span
                                    class="mat-mdc-button-touch-target"></span><span
                                    class="mat-ripple mat-mdc-button-ripple"></span></a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>