.sidenav-container {
  height: 100%;
}

.sidenav {
  width: 200px;
}

.sidenav .mat-toolbar {
  background: inherit;
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 1;
}


.lockScreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: rgba(76, 76, 76, 0.852);

  figure {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin: 1em 0;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
}

.mat-toolbar{
  background-color: #fafafa;
}