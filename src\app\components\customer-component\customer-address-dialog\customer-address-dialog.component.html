<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        Customer Address Form
    </h6>
    <form class="mt-3" [formGroup]="addressForm" (ngSubmit)="addNewAddress(addressForm)">
        <mat-form-field appearance="outline">
            <mat-label>Area</mat-label>
            <mat-select formControlName="areaId">
                <input type="text" autocomplete="off" matInput appSelectSearch [searchList]="AREA_DATA()?.data||[]"
                    [filterKeys]="['areaName','branchName']" class="px-3 py-4 mat-filter-menu"
                    style="font-size: 16px;font-weight: 400;" placeholder="Search..." />
                <mat-option *ngFor="let item of AREA_DATA()?.data" [value]="item.id">
                    {{item.areaName }} - {{item.branchName}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Address</mat-label>
            <input matInput type="text" placeholder="Enter Customer Address" formControlName="adress">
        </mat-form-field>

        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="addressForm.invalid" color="success">Create</button>
        </mat-dialog-actions>
    </form>
</div>