<div class="p-5">
    <div class="cdk-global-overlay-wrapper w-100 h-100" dir="ltr" style="justify-content: center; align-items: center;">
        <div id="cdk-overlay-5" class="cdk-overlay-pane w-100 h-100" style=" position: static;">
            <div tabindex="-1" class="mat-mdc-dialog-container mdc-dialog cdk-dialog-container mdc-dialog--open"
                id="mat-mdc-dialog-1" role="dialog" aria-modal="true" aria-labelledby="mat-mdc-dialog-title-1"
                style="--mat-dialog-transition-duration: 0ms;">
                <div class="mdc-dialog__container">
                    <div class="mat-mdc-dialog-surface mdc-dialog__surface">
                        <div class="ng-star-inserted">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 mat-dialog-title=""
                                    class="mat-mdc-dialog-title mdc-dialog__title mat-subtitle-1 ps-3">
                                    Update Subscription Header Info</h5>
                                <mat-icon class="text-secondary close-modal-button me-3 pointer" (click)="closeModal()"
                                    matSuffix>
                                    close</mat-icon>
                            </div>
                            <div class="px-5 py-3">
                                <form [formGroup]="editForm" (ngSubmit)="onSubmit(editForm)">
                                    <div class="row">
                                        <div class="col-12">
                                            <mat-form-field class="w-100 fs-16" appearance="outline">
                                                <mat-label>Name</mat-label>
                                                <input matInput type="text" placeholder="Enter customer name"
                                                    formControlName="customerName">
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    <div
                                        class="mat-mdc-dialog-actions mdc-dialog__actions p-24 p-t-0 justify-content-end">
                                        <button mat-raised-button color="success"
                                            [disabled]="editForm.invalid">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>