import { Injectable } from "@angular/core";
import { ApiConfigService } from "../core/api-config.service";
import { IAreaResponse } from "../interfaces/location.interface";
import { Observable } from "rxjs";
import { Subscription } from "../interfaces/pickup.Interface";

@Injectable({
  providedIn: 'root'
})

export class pickupService {

  constructor(private _ApiConfigService: ApiConfigService) { }

  getSubscrption(sid: number): Observable<any> {
    return this._ApiConfigService.getReq(`v1/Pickup/GetPickup?SID=${sid}`);
  }
  getSubscrptionByPhone(phone: string): Observable<any> {
    return this._ApiConfigService.getReq(`v1/Pickup/GetPickUpByPhone?phone=${phone}`);
  }

  pickup(sid: number, date: string) {
    return this._ApiConfigService.putReq(`v1/Pickup/Pickup?sid=${sid}&date=${date}`, {})
  }
}