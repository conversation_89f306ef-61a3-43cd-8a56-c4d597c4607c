/* Container to hold the entire layout */
.container {
  padding: 20px;
}

/* Details section style */
.details {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.details p {
  margin: 10px 0;
}

/* Status button with icon */
.status-button {
  color: white;
  font-size: 14px;
  width: 100px;
  height: 30px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 25px; /* Rounded corners for smooth radius */
  padding: 10px;
  background-color: #ff6f00; /* Button background color */
  border: none;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.status-button mat-icon {
  margin-right: 8px; /* Space between the icon and the text */
}

.status-button:hover {
  background-color: #0056b3; /* Change button color on hover */
  transform: scale(1.05); /* Slight scale effect on hover */
  cursor: pointer;
}

.status-button:focus {
  outline: none; /* Remove default outline on focus */
}
/* Styling for the card container */
.subscription-card {
  margin: 20px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ensuring the text and icon align well */
.property {
  display: flex;
  align-items: center; /* Align icon and text vertically */
  margin-bottom: 15px;
}

.property mat-icon {
  margin-right: 10px; /* Space between the icon and the text */
  font-size: 24px; /* Icon size */
  color: #e08427; /* Icon color */
}

/* Grid layout for two columns (left and right) */
.row {
  display: flex;
  flex-wrap: wrap;
}

.col-12 {
  width: 100%; /* Full width on small screens */
}

.col-md-6 {
  width: 50%; /* Each column will take half of the screen on medium and larger screens */
  padding: 10px;
}

/* Adjust the layout for better spacing on small screens */
@media (max-width: 767px) {
  .col-md-6 {
    width: 100%; /* Full width on smaller screens */
  }}

  /* Styling for the card container */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-evenly;
}
.sid-card{
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content:center;
}
/* Styling for the mat-card */
.meal-card {
  width: 250px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

/* Styling for the button */
.pickup-button {
  width: 100%;
  margin-top: 10px;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  border-radius: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.pickup-button:hover {
  background-color: #0056b3;
  cursor: pointer;
  transform: translateY(-2px);
}

.pickup-button:disabled {
  background-color: gray;
  cursor: not-allowed;
}

/* Optional: Add some subtle transition effect to the card or button */
.meal-card:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}
mat-card-footer {
  display: flex;
  justify-content: flex-end;
}

button {
  margin: 20px;
}

/* Center the dialog */
::ng-deep .mat-dialog-container {
  position: absolute;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 400px; /* Adjust the width as needed */
}

/* Gray backdrop for the dialog */
.gray-backdrop {
  background-color: rgba(0, 0, 0, 0.5); /* Gray background */
}

/* Optional: Customize the dialog box */
.mat-dialog-container {
  background-color: white; /* Background color of the dialog box */
  padding: 20px;
  border-radius: 8px;
}
.pickup-card {
  display: flex;
  max-width: 400px;
  margin: 20px auto;
}

.pickup-details {
  display: flex;
  flex-direction: column;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.detail-item mat-icon {
  margin-right: 10px;
}

.dark-appearance {
  background-color: #333;
  color: #fff;
}

::ng-deep .mat-mdc-form-field-flex{
  height: 50px !important;
}

.prepared{
  width: 25%;
  // margin-top: 10px;
  font-size: 14px;
  text-align: center;
  padding: 5px;
  border-radius: 20px;
  background-color: #1acc40;
  color: white;
  border: none;
  transition: all 0.3s ease;
  height: 35px;
}
.pickedup{
  width: 25%;
  // margin-top: 10px;
  font-size: 14px;
  text-align: center;
  padding: 5px;
  border-radius: 20px;
  background-color: #e99917;
  color: white;
  border: none;
  transition: all 0.3s ease;
  height: 35px;
}