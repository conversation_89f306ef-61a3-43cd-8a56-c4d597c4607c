<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        Update Form <span class="text-primary">(CHANGE DAY STATE)</span>
    </h6>
    <form class="mt-3" [formGroup]="dialogForm" (ngSubmit)="onSubmit(dialogForm)">
        <mat-form-field appearance="outline">
            <mat-label>Select Day State</mat-label>
            <mat-select formControlName="state">
                <mat-option *ngFor="let item of State" [value]="item">
                    {{item}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="dialogForm.invalid" color="success">Update</button>
        </mat-dialog-actions>
    </form>
</div>