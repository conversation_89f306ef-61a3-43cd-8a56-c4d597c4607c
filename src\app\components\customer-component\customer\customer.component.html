<div class="my-4">
    <mat-card class="w-100">
        <mat-card-content>
            <div class="flex-between mb-4">
                <h4 class="mb-3">Table Filters</h4>
                <div>
                    <button mat-raised-button color="help" class="mx-1" (click)="export()"
                        [disabled]="!Permissions.Customers.Export">Export Filter</button>
                    <button mat-raised-button class="ms-8" [disabled]="!Permissions.Customers.Create"
                        (click)="openCreateDialog()" color="success">Add New
                        Customer</button>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <mat-form-field class="mx-1" appearance="outline">
                        <mat-label>Search By Customer Phone</mat-label>
                        <input matInput [disabled]="!Permissions.Customers.Search"
                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                            (keyup.enter)="filter(Phone)" (blur)="filter(Phone)" placeholder="Ex: +02" #Phone>
                    </mat-form-field>
                </div>
            </div>
        </mat-card-content>
    </mat-card>
    <mat-card class="w-100">
        <mat-card-content>
            <h4 class="mb-56">Customers Table</h4>
            <generic-table [TABLE_DATA]="TABLE_DATA()" [columns]="columns" [pageSizeOptions]="[10, 25, 50, 100, 500]"
                [actions]="actions"
                [components]="{filter: true,columns: true,export: Permissions.Customers.Export,selection: false, index:true}"
                (PaginateOptions)="Paginate($event)" (update)="updateRow($event)" (delete)="deleteRow($event)"
                (sub)="createSubscription($event)">
            </generic-table>
        </mat-card-content>
    </mat-card>
</div>