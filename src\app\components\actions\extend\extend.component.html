<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        Action Form <span class="text-primary">(EXTEND)</span>
    </h6>
    <form class="mt-3" [formGroup]="dialogForm" (ngSubmit)="onSubmit(dialogForm)">

        <mat-form-field appearance="outline">
            <mat-label>Duration</mat-label>
            <input matInput placeholder="Enter Duration" formControlName="DaysCount" required maxlength="4"
                [matAutocomplete]="auto" type="number"
                oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
            <mat-autocomplete  #auto="matAutocomplete">
                <mat-option *ngFor="let item of []" [value]="item">{{item}}</mat-option>
            </mat-autocomplete>
        </mat-form-field>

        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Notes</mat-label>
            <textarea matInput rows="3" placeholder="Write your notes" value="Default Value"
                formControlName="Notes"></textarea>
        </mat-form-field>

        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="dialogForm.invalid" color="success">Submit</button>
        </mat-dialog-actions>
    </form>
</div>