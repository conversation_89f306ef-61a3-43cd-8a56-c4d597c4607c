export enum TaxDiscountOptionEnum {
  ApplyBeforDiscount = "Apply Befor Discount",
  ApplyAfterDiscount = "Apply After Discount",
}

export enum SubscribeOptionsEnum {
  Web = 0,
  "Mobile Application" = 1,
  Branch = 2,
}

export enum DeliveryStatusEnum {
  Deliveried = "Deliveried",
  Resticited = "Resticited",
  Pending = "Pending",
  Hold = "Hold",
  Canceld = "Canceld",
  PickedUp="PickedUp",
  Prepared=" Prepared"
}

export enum DeliveryStatusIndexEnum {
  Pending, 
  Deliveried,
  NotDelivered,
  Hold, Resticited,
  Canceld, 
  AllStatus,
  PickedUp, 
  Refund,
  Prepared
}

export enum LineStateEnum {
  Done = "Done",
  Pending = "Pending",
  Cancel = "Cancel",
}

export enum SubscriptionStatusEnum {
  Active = "Active",
  Expired = "Expired",
  Hold = "Hold",
}
