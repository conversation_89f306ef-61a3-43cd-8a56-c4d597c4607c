<div class="px-5">
    <h6 mat-dialog-title class="px-0">
        Plan Price Form
    </h6>
    <form class="mt-3" [formGroup]="priceForm" (ngSubmit)="onSubmit(priceForm)">
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Day Count</mat-label>
            <input matInput formControlName="dayName">
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline" *ngFor="let control of getControls()">
            <mat-label>{{control}}</mat-label>
            <input matInput type="number" [formControlName]="control"
                oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');">
        </mat-form-field>

        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="priceForm.invalid" color="success">Confirm Price</button>
        </mat-dialog-actions>
    </form>
</div>