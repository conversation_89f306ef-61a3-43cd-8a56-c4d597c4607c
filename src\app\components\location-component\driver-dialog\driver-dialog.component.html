<div class="px-5">
    <form class="mt-3" [formGroup]="dialogForm" (ngSubmit)="onSubmit(dialogForm)">
        <div class="flex-between align-items-end mb-3">
            <h6 mat-dialog-title class="px-0">
                Driver Form
            </h6>
            <mat-slide-toggle class="mx-3 mb-2" formControlName="active">
                Active?</mat-slide-toggle>
        </div>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Driver Name</mat-label>
            <input matInput type="text" placeholder="Enter City Name" formControlName="driverName">
        </mat-form-field>
        <mat-form-field appearance="outline">
            <mat-label>Branch</mat-label>
            <mat-select formControlName="branchid">
                <input type="text" autocomplete="off" matInput appSelectSearch [searchList]="BRANCH_DATA()?.data || []"
                    [filterKeys]="['branchName']" class="px-3 py-4 mat-filter-menu"
                    style="font-size: 16px;font-weight: 400;" placeholder="Search..." />
                <mat-option *ngFor="let item of BRANCH_DATA()?.data" [value]="item.branchID">
                    {{item.branchName}}
                </mat-option>
            </mat-select>
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Phone (1)</mat-label>
            <input matInput type="text" placeholder="Enter phone (1)" formControlName="phone1">
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Phone (2)</mat-label>
            <input matInput type="text" placeholder="Enter phone (2)" formControlName="phone2">
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Phone (3)</mat-label>
            <input matInput type="text" placeholder="Enter phone (3)" formControlName="phone3">
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Address</mat-label>
            <input matInput type="text" placeholder="Enter address" formControlName="adress">
        </mat-form-field>
        <mat-form-field class="w-100 fs-16" appearance="outline">
            <mat-label>Notes</mat-label>
            <textarea matInput rows="3" placeholder="Write your notes" formControlName="notes"></textarea>
        </mat-form-field>

        <mat-dialog-actions align="end">
            <button type="button" mat-button mat-dialog-close>Cancel</button>
            <button type="submit" mat-raised-button [disabled]="dialogForm.invalid" color="success">Submit</button>
        </mat-dialog-actions>
    </form>
</div>