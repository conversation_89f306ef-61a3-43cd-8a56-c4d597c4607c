import { NgxUiLoaderConfig } from 'ngx-ui-loader';

export const ngxUiLoaderConfig: NgxUiLoaderConfig = {
    "bgsColor": "rgba(0, 0, 0, 0.0)",
    "bgsOpacity": 1,
    "bgsPosition": "bottom-right",
    "bgsSize": 150,
    "bgsType": "ball-scale-multiple",
    "blur": 15,
    "delay": 0,
    "fastFadeOut": true,
    "fgsColor": "rgba(0, 0, 0, 0.0)",
    "fgsPosition": "center-center",
    "fgsSize": 20,
    "fgsType": "three-strings",
    "gap": 25,
    "logoPosition": "center-center",
    "logoSize": 80,
    "masterLoaderId": "master",
    "overlayBorderRadius": "0",
    // "overlayColor": "rgba(40, 40, 40, 0.8)",
    "overlayColor": "rgba(0, 0, 0, 0.0)",
    // "pbColor": "#fff",
    "pbColor": "#fb9778",
    "pbDirection": "ltr",
    "pbThickness": 3,
    "hasProgressBar": true,
    "text": "Loading",
    "textColor": "rgba(0, 0, 0, 0.0)",
    "textPosition": "center-center",
    "maxTime": -1,
    "minTime": 300
}

// demo https://tdev.app/ngx-ui-loader/demo/
// demo https://tdev.app/ngx-ui-loader

