<div class="my-5 mx-5">
    <mat-card class="w-100">
        <mat-card-content>
            <h6 mat-dialog-title class="px-0">
                CREATE CUSTOM MEAL
            </h6>
            <div class="text-end">
                <button mat-raised-button class="me-8 mb-8" color="success" (click)="openItemDialog()">
                    <mat-icon>library_add</mat-icon>
                    Add Item
                </button>
            </div>

            <mat-form-field class="w-100 fs-16" appearance="outline" *ngIf="MealDetails.length">
                <mat-label>Full Meal Name</mat-label>
                <input matInput type="text" [disabled]="true" [ngModel]="getConcatenatedItemNames()">
            </mat-form-field>


            <generic-table *ngIf="displayTable" [TABLE_DATA]="{data:MealDetails}" [columns]="columns"
                [actions]="actions" [components]="{reorder:true,footer:true}" (reorderData)="reorder($event)"
                (delete)="deleteRow($event)" (update)="updateRow($event)">
            </generic-table>

            <mat-dialog-actions align="end">
                <button color="warn" type="button" mat-button mat-dialog-close>Cancel</button>
                <button color="primary" type="button" mat-raised-button (click)="createMeal()">Confirm</button>
            </mat-dialog-actions>
        </mat-card-content>
    </mat-card>
</div>